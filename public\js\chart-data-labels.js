// 图表数据标签显示/隐藏控制器
class ChartDataLabelsController {
    constructor() {
        this.dataLabelsVisible = {}; // 存储每个图表的标签可见状态
        this.init();
    }

    init() {
        // 注册全局Chart.js插件
        if (typeof Chart !== 'undefined') {
            Chart.register({
                id: 'dataLabelsPlugin',
                afterDatasetsDraw: (chart) => {
                    // 确保不处理气泡图
                    if (chart.config.type === 'bubble' || 
                        chart.canvas.id.includes('bubble') || 
                        chart.canvas.id.includes('Bubble')) {
                        return;
                    }
                    
                    if (this.dataLabelsVisible[chart.canvas.id]) {
                        this.drawDataLabels(chart);
                    }
                }
            });
        }

        // 监听页面加载完成后添加控制按钮
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => this.addToggleButtons(), 1000);
            });
        } else {
            setTimeout(() => this.addToggleButtons(), 1000);
        }
    }

    // 绘制数据标签
    drawDataLabels(chart) {
        const ctx = chart.ctx;
        const chartType = chart.config.type;

        // 排除气泡图和其他不需要数据标签的图表类型
        if (chartType === 'bubble' || chartType === 'scatter' || chartType === 'doughnut' || chartType === 'pie') {
            return;
        }

        ctx.save();
        ctx.textAlign = 'center';
        ctx.textBaseline = 'bottom';
        ctx.font = 'bold 11px Arial';
        ctx.fillStyle = '#333';

        chart.data.datasets.forEach((dataset, datasetIndex) => {
            const meta = chart.getDatasetMeta(datasetIndex);
            
            // 排除气泡图数据集
            if (!meta.hidden && dataset.type !== 'bubble') {
                dataset.data.forEach((dataPoint, index) => {
                    const element = meta.data[index];
                    if (element && element.x !== undefined && element.y !== undefined) {
                        
                        // 获取显示的值（使用tooltip的格式化逻辑）
                        let displayValue = this.getFormattedValue(chart, datasetIndex, index, dataPoint);
                        
                        if (displayValue) {
                            this.drawLabel(ctx, element, displayValue, dataset.type || chartType, chart);
                        }
                    }
                });
            }
        });

        ctx.restore();
    }

    // 获取格式化的值
    getFormattedValue(chart, datasetIndex, index, dataPoint) {
        const dataset = chart.data.datasets[datasetIndex];
        const config = chart.config;

        try {
            // 尝试使用tooltip回调获取格式化的值
            if (config.options?.plugins?.tooltip?.callbacks?.label) {
                const context = {
                    dataset: dataset,
                    datasetIndex: datasetIndex,
                    dataIndex: index,
                    parsed: { y: dataPoint, x: index },
                    label: chart.data.labels?.[index] || ''
                };
                
                let formattedValue = config.options.plugins.tooltip.callbacks.label(context);
                
                // 移除数据集标签前缀
                if (typeof formattedValue === 'string') {
                    const colonIndex = formattedValue.indexOf(':');
                    if (colonIndex !== -1) {
                        formattedValue = formattedValue.substring(colonIndex + 1).trim();
                    }
                }
                
                return formattedValue;
            }
        } catch (e) {
            console.warn('无法获取tooltip格式化值:', e);
        }

        // 备用格式化逻辑
        if (typeof dataPoint === 'number') {
            if (dataset.label && (dataset.label.includes('增长率') || dataset.label.includes('QP') || dataset.label.includes('GR') ||
                dataset.label.includes('同比') || dataset.label.includes('环比'))) {
                // 确保增长率数值显示正负号，以便正确应用颜色
                return `${dataPoint >= 0 ? '+' : ''}${dataPoint.toFixed(1)}%`;
            } else if (dataset.label && dataset.label.includes('销售额')) {
                if (dataPoint >= 10000) {
                    return `¥${(dataPoint / 10000).toFixed(1)}万`;
                } else if (dataPoint >= 1000) {
                    return `¥${(dataPoint / 1000).toFixed(1)}K`;
                } else {
                    return `¥${dataPoint.toFixed(0)}`;
                }
            } else if (dataPoint >= 1000) {
                return dataPoint.toLocaleString();
            } else {
                return dataPoint.toFixed(1);
            }
        }

        return String(dataPoint);
    }

    // 绘制标签
    drawLabel(ctx, element, text, type, chart) {
        let x = element.x;
        let y = element.y;

        // 根据图表类型调整标签位置
        if (type === 'bar') {
            // 柱状图：标签显示在柱子上方
            const barHeight = Math.abs(element.y - element.base);
            if (barHeight < 20) {
                // 如果柱子太短，标签显示在柱子外侧
                y = Math.min(element.y, element.base) - 15;
            } else {
                // 标签显示在柱子顶部上方
                y = y - 8;
            }
        } else if (type === 'line') {
            // 线图：标签显示在点的上方
            y = y - 15;
        }

        // 确保标签不超出图表区域
        const chartArea = chart.chartArea;
        if (y < chartArea.top + 20) {
            // 如果上方空间不够，显示在下方
            if (type === 'bar') {
                y = Math.max(element.y, element.base) + 20;
            } else {
                y = element.y + 20;
            }
            ctx.textBaseline = 'top';
        } else {
            ctx.textBaseline = 'bottom';
        }

        // 绘制文字背景
        const textMetrics = ctx.measureText(text);
        const textWidth = textMetrics.width;
        const textHeight = 14;
        const padding = 3;

        // 根据数据类型选择背景颜色
        let bgColor = 'rgba(255, 255, 255, 0.95)';
        let borderColor = 'rgba(0, 0, 0, 0.2)';

        // 检查是否为增长率或包含正负号的数值
        if (text.includes('%') || text.includes('+') || text.includes('-')) {
            if (text.includes('+') || (text.includes('%') && !text.includes('-') && parseFloat(text) > 0)) {
                bgColor = 'rgba(34, 197, 94, 0.15)'; // 绿色背景表示正增长
                borderColor = 'rgba(34, 197, 94, 0.4)';
            } else if (text.includes('-') || (text.includes('%') && parseFloat(text) < 0)) {
                bgColor = 'rgba(239, 68, 68, 0.15)'; // 红色背景表示负增长
                borderColor = 'rgba(239, 68, 68, 0.4)';
            }
        }

        ctx.fillStyle = bgColor;
        ctx.fillRect(
            x - textWidth/2 - padding, 
            y - textHeight - padding, 
            textWidth + padding * 2, 
            textHeight + padding * 2
        );

        // 绘制边框
        ctx.strokeStyle = borderColor;
        ctx.lineWidth = 1;
        ctx.strokeRect(
            x - textWidth/2 - padding, 
            y - textHeight - padding, 
            textWidth + padding * 2, 
            textHeight + padding * 2
        );

        // 绘制文字 - 根据正负值设置颜色
        if (text.includes('+') || (text.includes('%') && !text.includes('-') && parseFloat(text) > 0)) {
            ctx.fillStyle = '#16a34a'; // 绿色文字表示正数/正增长
        } else if (text.includes('-') || (text.includes('%') && parseFloat(text) < 0)) {
            ctx.fillStyle = '#dc2626'; // 红色文字表示负数/负增长
        } else {
            ctx.fillStyle = '#374151'; // 默认深灰色
        }
        
        ctx.fillText(text, x, y);
    }

    // 添加控制按钮
    addToggleButtons() {
        const chartContainers = this.findChartContainers();
        
        chartContainers.forEach(container => {
            if (!container.querySelector('.data-labels-toggle')) {
                this.createToggleButton(container);
            }
        });
    }

    // 查找图表容器
    findChartContainers() {
        const containers = [];
        
        // 查找各种可能的图表容器，但排除气泡图容器
        const selectors = [
            '.chart-container:not(.bubble-chart-container)',
            '.monthly-comparison-chart-container', 
            '.chart-item:not(.bubble-chart-container)',
            '[id$="Chart"]:not([id*="bubble"])',
            'canvas:not([id*="bubble"])'
        ];

        selectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(element => {
                // 额外检查，确保不是气泡图相关
                if (element.id && (element.id.includes('bubble') || element.id.includes('Bubble'))) {
                    return;
                }
                if (element.className && element.className.includes('bubble')) {
                    return;
                }

                // 如果是canvas，找其父容器
                if (element.tagName.toLowerCase() === 'canvas') {
                    const parent = element.closest('.chart-container, .chart-item, .monthly-comparison-chart-container') || element.parentElement;
                    if (parent && !containers.includes(parent) && 
                        !parent.className.includes('bubble') && 
                        !parent.id.includes('bubble')) {
                        containers.push(parent);
                    }
                } else if (!containers.includes(element)) {
                    // 检查是否包含canvas
                    const canvas = element.querySelector('canvas');
                    if (canvas && !canvas.id.includes('bubble') && !canvas.id.includes('Bubble')) {
                        containers.push(element);
                    }
                }
            });
        });

        return containers;
    }

    // 创建切换按钮
    createToggleButton(container) {
        const canvas = container.querySelector('canvas');
        if (!canvas) return;

        // 检查是否是气泡图容器，如果是则跳过
        if (container.classList.contains('bubble-chart-container') || 
            container.id.includes('bubble') || 
            canvas.id.includes('bubble')) {
            return;
        }

        // 通过检查图表实例来确定是否是气泡图
        if (window.Chart && Chart.instances) {
            const chartInstance = Object.values(Chart.instances).find(chart => 
                chart.canvas && chart.canvas.id === canvas.id
            );
            if (chartInstance && chartInstance.config.type === 'bubble') {
                return;
            }
        }

        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'data-labels-toggle';
        buttonContainer.style.cssText = `
            position: absolute;
            top: 5px;
            right: 5px;
            z-index: 10;
        `;

        const button = document.createElement('button');
        button.className = 'btn btn-sm btn-outline-secondary';
        button.style.cssText = `
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid #ddd;
            cursor: pointer;
        `;
        
        button.textContent = '显示数据';
        button.title = '点击显示/隐藏数据标签';

        // 确保容器有相对定位
        if (getComputedStyle(container).position === 'static') {
            container.style.position = 'relative';
        }

        // 添加点击事件
        button.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation(); // 确保不影响其他事件
            this.toggleDataLabels(canvas.id, button);
        });

        buttonContainer.appendChild(button);
        container.appendChild(buttonContainer);

        // 初始化状态
        this.dataLabelsVisible[canvas.id] = false;
    }

    // 切换数据标签显示状态
    toggleDataLabels(canvasId, button) {
        const isVisible = this.dataLabelsVisible[canvasId];
        this.dataLabelsVisible[canvasId] = !isVisible;

        // 更新按钮文字和样式
        if (this.dataLabelsVisible[canvasId]) {
            button.textContent = '隐藏数据';
            button.classList.remove('btn-outline-secondary');
            button.classList.add('btn-secondary');
        } else {
            button.textContent = '显示数据';
            button.classList.remove('btn-secondary');
            button.classList.add('btn-outline-secondary');
        }

        // 查找对应的图表实例并重新绘制
        this.redrawChart(canvasId);
    }

    // 重新绘制图表
    redrawChart(canvasId) {
        // 尝试从各种可能的全局变量中找到图表实例
        const possibleChartVars = [
            'monthlyComparisonChart',
            'momComparisonChart', 
            'hospitalComparisonChart',
            'hospitalMomChart',
            'salesTargetChart',
            'modalBubbleChartInstance'
        ];

        // 检查window对象上的图表实例
        for (let varName of possibleChartVars) {
            if (window[varName] && window[varName].canvas && window[varName].canvas.id === canvasId) {
                window[varName].update();
                return;
            }
        }

        // 检查charts对象
        if (window.charts && window.charts[canvasId]) {
            window.charts[canvasId].update();
            return;
        }

        // 如果找不到具体实例，触发所有Chart.js实例的更新
        if (typeof Chart !== 'undefined' && Chart.instances) {
            Object.values(Chart.instances).forEach(chart => {
                if (chart.canvas && chart.canvas.id === canvasId) {
                    chart.update();
                }
            });
        }
    }

    // 手动刷新所有按钮（用于动态加载的图表）
    refreshButtons() {
        setTimeout(() => this.addToggleButtons(), 100);
    }
}

// 创建全局实例
window.chartDataLabelsController = new ChartDataLabelsController();

// 监听页面变化，自动添加按钮
if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver((mutations) => {
        let shouldRefresh = false;
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) {
                        // 检查是否是canvas且不是气泡图
                        if (node.tagName.toLowerCase() === 'canvas') {
                            if (!node.id.includes('bubble') && !node.id.includes('Bubble')) {
                                shouldRefresh = true;
                            }
                        }
                        // 检查是否包含canvas且不是气泡图容器
                        else if (node.querySelector && !node.className.includes('bubble') && !node.id.includes('bubble')) {
                            const canvas = node.querySelector('canvas:not([id*="bubble"]):not([id*="Bubble"])');
                            if (canvas) {
                                shouldRefresh = true;
                            }
                        }
                    }
                });
            }
        });
        
        if (shouldRefresh) {
            window.chartDataLabelsController.refreshButtons();
        }
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
} 